import { json } from '@sveltejs/kit';
import { movieApi } from '$lib/services/movieApi.js';

/** @type {import('./$types').RequestHandler} */
export async function GET({ url }) {
    try {
        const query = url.searchParams.get('q');
        const type = url.searchParams.get('type') || 'all'; // 'movie', 'tv', or 'all'
        const page = parseInt(url.searchParams.get('page') || '1');

        if (!query) {
            return json({
                success: false,
                error: 'Search query is required'
            }, { status: 400 });
        }

        let results = [];

        switch (type) {
            case 'movie':
                results = await movieApi.searchMovies(query, page);
                break;
            case 'tv':
                results = await movieApi.searchSeries(query, page);
                break;
            case 'all':
            default:
                results = await movieApi.search(query, '', page);
                break;
        }

        return json({
            success: true,
            data: results,
            query,
            type,
            page
        });

    } catch (error) {
        console.error('Search API Error:', error);
        return json({
            success: false,
            error: error.message
        }, { status: 500 });
    }
}
