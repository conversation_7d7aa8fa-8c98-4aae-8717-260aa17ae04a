{"name": "workspace", "version": "1.0.0", "type": "module", "main": "postcss.config.js", "scripts": {"dev": "node dev.mjs", "build": "vite build", "preview": "vite preview", "check": "svelte-kit sync && svelte-check --tsconfig ./jsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./jsconfig.json --watch", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@sveltejs/adapter-auto": "^6.0.2", "@sveltejs/kit": "^2.27.3", "@sveltejs/vite-plugin-svelte": "^6.1.0", "@tailwindcss/postcss": "^4.1.11", "autoprefixer": "^10.4.21", "lucide-svelte": "^0.537.0", "postcss": "^8.5.6", "svelte": "^5.38.0", "svelte-check": "^4.3.1", "tailwindcss": "^4.1.11", "vite": "^7.1.1"}}