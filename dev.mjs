#!/usr/bin/env node
import { readFileSync } from 'fs';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { createServer } from 'vite';
import { sveltekit } from '@sveltejs/kit/vite';

const __dirname = dirname(fileURLToPath(import.meta.url));

async function startDevServer() {
  try {
    const server = await createServer({
      plugins: [sveltekit()],
      server: {
        host: 'localhost',
        port: 5000
      },
      configFile: false,
      root: __dirname
    });

    await server.listen();
    console.log('🚀 SvelteKit dev server running on http://localhost:5000');
  } catch (error) {
    console.error('❌ Error starting server:', error);
    process.exit(1);
  }
}

startDevServer();