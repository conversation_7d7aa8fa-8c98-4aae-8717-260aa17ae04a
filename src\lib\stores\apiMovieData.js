import { writable } from 'svelte/store';
import { movieApi } from '../services/movieApi.js';

// Create reactive stores for movie data
export const movies = writable([]);
export const series = writable([]);
export const loading = writable(false);
export const error = writable(null);

// Store for different movie categories
export const featuredMovies = writable([]);
export const actionMovies = writable([]);
export const dramaMovies = writable([]);
export const sciFiMovies = writable([]);
export const topRatedMovies = writable([]);

// Store for different series categories
export const topRatedSeries = writable([]);
export const dramaSeries = writable([]);
export const comedySeries = writable([]);
export const sciFiSeries = writable([]);

// API data service
class ApiMovieDataService {
    constructor() {
        this.initialized = false;
    }

    // Initialize all data
    async initialize() {
        if (this.initialized) return;
        
        loading.set(true);
        error.set(null);

        try {
            // Load movies
            await this.loadMovies();
            
            // Load series
            await this.loadSeries();
            
            this.initialized = true;
        } catch (err) {
            console.error('Failed to initialize movie data:', err);
            error.set(err.message);
        } finally {
            loading.set(false);
        }
    }

    // Load movies from API
    async loadMovies() {
        try {
            // Get popular movies
            const popularMovies = await movieApi.getPopularMovies();
            movies.set(popularMovies);

            // Use popular movies as featured (high rated ones)
            const highRated = popularMovies.filter(movie => movie.rating >= 8.0);
            featuredMovies.set(highRated);
            topRatedMovies.set(highRated);

            // Get movies by genre
            const actionResults = await movieApi.getMoviesByGenre('action');
            actionMovies.set(actionResults);

            const dramaResults = await movieApi.getMoviesByGenre('drama');
            dramaMovies.set(dramaResults);

            const sciFiResults = await movieApi.getMoviesByGenre('sci-fi');
            sciFiMovies.set(sciFiResults);

        } catch (err) {
            console.error('Failed to load movies:', err);
            throw err;
        }
    }

    // Load series from API
    async loadSeries() {
        try {
            // Get popular series
            const popularSeries = await movieApi.getPopularSeries();
            series.set(popularSeries);

            // Use popular series as top rated (high rated ones)
            const highRated = popularSeries.filter(series => series.rating >= 8.0);
            topRatedSeries.set(highRated);

            // Filter by genre from popular series
            dramaSeries.set(popularSeries.filter(s => s.genre.toLowerCase().includes('drama')));
            comedySeries.set(popularSeries.filter(s => s.genre.toLowerCase().includes('comedy')));
            sciFiSeries.set(popularSeries.filter(s => s.genre.toLowerCase().includes('sci-fi')));

        } catch (err) {
            console.error('Failed to load series:', err);
            throw err;
        }
    }

    // Search functionality
    async searchMovies(query) {
        loading.set(true);
        try {
            const results = await movieApi.searchMovies(query);
            return results;
        } catch (err) {
            error.set(err.message);
            return [];
        } finally {
            loading.set(false);
        }
    }

    async searchSeries(query) {
        loading.set(true);
        try {
            const results = await movieApi.searchSeries(query);
            return results;
        } catch (err) {
            error.set(err.message);
            return [];
        } finally {
            loading.set(false);
        }
    }

    // Get trending content
    async getTrending() {
        try {
            const trending = await movieApi.getTrending();
            return trending;
        } catch (err) {
            error.set(err.message);
            return [];
        }
    }

    // Refresh data
    async refresh() {
        this.initialized = false;
        await this.initialize();
    }
}

export const apiMovieDataService = new ApiMovieDataService();

// Auto-initialize when imported (you can remove this if you prefer manual initialization)
if (typeof window !== 'undefined') {
    apiMovieDataService.initialize().catch(console.error);
}
