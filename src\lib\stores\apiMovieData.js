import { writable } from 'svelte/store';
import { movieApi } from '../services/movieApi.js';

// Create reactive stores for movie data
export const movies = writable([]);
export const series = writable([]);
export const loading = writable(false);
export const error = writable(null);

// Store for different movie categories
export const featuredMovies = writable([]);
export const actionMovies = writable([]);
export const dramaMovies = writable([]);
export const sciFiMovies = writable([]);
export const topRatedMovies = writable([]);

// Store for different series categories
export const topRatedSeries = writable([]);
export const dramaSeries = writable([]);
export const comedySeries = writable([]);
export const sciFiSeries = writable([]);

// API data service
class ApiMovieDataService {
    constructor() {
        this.initialized = false;
    }

    // Initialize all data
    async initialize() {
        if (this.initialized) return;
        
        loading.set(true);
        error.set(null);

        try {
            // Load movies
            await this.loadMovies();
            
            // Load series
            await this.loadSeries();
            
            this.initialized = true;
        } catch (err) {
            console.error('Failed to initialize movie data:', err);
            error.set(err.message);
        } finally {
            loading.set(false);
        }
    }

    // Load movies from API
    async loadMovies() {
        try {
            // Get popular movies
            const popularMovies = await movieApi.getPopularMovies();
            movies.set(popularMovies);

            // Get top rated movies for featured section
            const topRated = await movieApi.getTopRatedMovies();
            featuredMovies.set(topRated.slice(0, 10));
            topRatedMovies.set(topRated);

            // Get movies by genre
            const actionResults = await movieApi.getMoviesByGenre(28); // Action genre ID
            actionMovies.set(actionResults.slice(0, 10));

            const dramaResults = await movieApi.getMoviesByGenre(18); // Drama genre ID
            dramaMovies.set(dramaResults.slice(0, 10));

            const sciFiResults = await movieApi.getMoviesByGenre(878); // Sci-Fi genre ID
            sciFiMovies.set(sciFiResults.slice(0, 10));

        } catch (err) {
            console.error('Failed to load movies:', err);
            throw err;
        }
    }

    // Load series from API
    async loadSeries() {
        try {
            // Get popular series
            const popularSeries = await movieApi.getPopularSeries();
            series.set(popularSeries);

            // Get top rated series
            const topRated = await movieApi.getTopRatedSeries();
            topRatedSeries.set(topRated.slice(0, 10));

            // Note: TMDB doesn't have genre-specific endpoints for TV shows like movies
            // So we'll filter from popular series or use discover endpoint
            const allSeries = [...popularSeries, ...topRated];
            
            // Filter by genre (approximate - you might want to improve this)
            dramaSeries.set(allSeries.filter(s => s.genre.toLowerCase().includes('drama')).slice(0, 10));
            comedySeries.set(allSeries.filter(s => s.genre.toLowerCase().includes('comedy')).slice(0, 10));
            sciFiSeries.set(allSeries.filter(s => s.genre.toLowerCase().includes('sci-fi')).slice(0, 10));

        } catch (err) {
            console.error('Failed to load series:', err);
            throw err;
        }
    }

    // Search functionality
    async searchMovies(query) {
        loading.set(true);
        try {
            const results = await movieApi.searchMovies(query);
            return results;
        } catch (err) {
            error.set(err.message);
            return [];
        } finally {
            loading.set(false);
        }
    }

    async searchSeries(query) {
        loading.set(true);
        try {
            const results = await movieApi.searchSeries(query);
            return results;
        } catch (err) {
            error.set(err.message);
            return [];
        } finally {
            loading.set(false);
        }
    }

    // Get trending content
    async getTrending() {
        try {
            const trending = await movieApi.getTrending();
            return trending;
        } catch (err) {
            error.set(err.message);
            return [];
        }
    }

    // Refresh data
    async refresh() {
        this.initialized = false;
        await this.initialize();
    }
}

export const apiMovieDataService = new ApiMovieDataService();

// Auto-initialize when imported (you can remove this if you prefer manual initialization)
if (typeof window !== 'undefined') {
    apiMovieDataService.initialize().catch(console.error);
}
