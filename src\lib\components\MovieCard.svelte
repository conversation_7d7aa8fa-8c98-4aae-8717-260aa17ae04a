<script>
        export let movie;
        
        let imageLoaded = false;
        let imageError = false;
        
        function handleImageLoad() {
                imageLoaded = true;
        }
        
        function handleImageError() {
                imageError = true;
                imageLoaded = true;
        }
</script>

<div class="relative bg-white rounded-lg overflow-hidden shadow-lg transition-shadow duration-300 border border-slate-100">
        <!-- Movie Poster -->
        <div class="relative aspect-[2/3] overflow-hidden">
                {#if !imageLoaded}
                        <div class="absolute inset-0 bg-slate-200 animate-pulse flex items-center justify-center">
                                <svg class="w-12 h-12 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                </svg>
                        </div>
                {/if}
                
                {#if imageError}
                        <div class="absolute inset-0 bg-slate-100 flex flex-col items-center justify-center text-slate-500">
                                <svg class="w-12 h-12 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                </svg>
                                <span class="text-sm">No Image</span>
                        </div>
                {:else}
                        <div class="group relative w-full h-full">
                                <img
                                        src={movie.poster}
                                        alt={movie.title}
                                        class="w-full h-full object-cover transition-all duration-300 cursor-pointer hover:scale-110 {imageLoaded ? 'opacity-100' : 'opacity-0'}"
                                        on:load={handleImageLoad}
                                        on:error={handleImageError}
                                        loading="lazy"
                                />

                                <!-- Hover Overlay -->
                                <div class="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end">
                                        <div class="p-4 w-full">
                                                <button class="w-full bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-lg font-semibold transition-colors duration-300 transform translate-y-4 group-hover:translate-y-0">
                                                        Play Now
                                                </button>
                                        </div>
                                </div>
                        </div>
                {/if}
                
                <!-- Rating Badge -->
                <div class="absolute top-2 right-2 bg-black/70 backdrop-blur-sm text-white px-2 py-1 rounded-lg text-sm font-semibold">
                        ⭐ {movie.rating}
                </div>
                
                <!-- Quality Badge -->
                {#if movie.quality}
                        <div class="absolute top-2 left-2 bg-blue-500 text-white px-2 py-1 rounded text-xs font-bold">
                                {movie.quality}
                        </div>
                {/if}
        </div>
        
        <!-- Movie Info -->
        <div class="p-4">
                <h3 class="text-slate-800 font-semibold text-sm md:text-base mb-1 line-clamp-2 transition-colors duration-300">
                        {movie.title}
                </h3>
                <div class="flex items-center justify-between text-slate-500 text-xs md:text-sm">
                        <span>{movie.year}</span>
                        <span class="bg-slate-100 text-slate-600 px-2 py-1 rounded text-xs">{movie.genre}</span>
                </div>
                {#if movie.duration}
                        <div class="text-gray-500 text-xs mt-1">
                                {movie.duration}
                        </div>
                {/if}
        </div>
</div>

<style>
        .line-clamp-2 {
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                overflow: hidden;
        }
</style>
