<script>
        import MovieCard from '$lib/components/MovieCard.svelte';
        import HorizontalCarousel from '$lib/components/HorizontalCarousel.svelte';
        import { movieData } from '$lib/stores/movieData.js';
        
        let searchTerm = '';
        let selectedGenre = 'all';
        
        const genres = ['all', 'drama', 'sci-fi', 'comedy', 'thriller', 'action', 'fantasy'];
        
        $: filteredSeries = movieData.series.filter(series => {
                const matchesSearch = series.title.toLowerCase().includes(searchTerm.toLowerCase());
                const matchesGenre = selectedGenre === 'all' || series.genre.toLowerCase() === selectedGenre;
                return matchesSearch && matchesGenre;
        });
        
        // Create carousel sections
        $: topRatedSeries = movieData.series.filter(series => series.rating >= 8.0);
        $: dramaSeries = movieData.series.filter(series => series.genre.toLowerCase() === 'drama');
        $: comedySeries = movieData.series.filter(series => series.genre.toLowerCase() === 'comedy');
        $: sciFiSeries = movieData.series.filter(series => series.genre.toLowerCase() === 'sci-fi');
</script>

<svelte:head>
        <title>Series - CineMax</title>
        <meta name="description" content="Binge-watch our premium collection of TV series and shows." />
</svelte:head>

<div class="min-h-screen pt-8 md:pt-24 px-4 md:px-8 bg-gradient-to-br from-slate-50 to-slate-100">
        <div class="max-w-7xl mx-auto">
                <!-- Header -->
                <div class="mb-8">
                        <h1 class="text-4xl md:text-5xl font-bold text-slate-800 mb-4">TV Series</h1>
                        <p class="text-xl text-slate-600">Binge-watch our premium series collection</p>
                </div>

                <!-- Featured Carousels -->
                <div class="space-y-12 mb-12">
                        <HorizontalCarousel 
                                movies={topRatedSeries}
                                title="⭐ Top Rated Series"
                                autoScroll={true}
                                scrollInterval={5000}
                        />
                        
                        <HorizontalCarousel 
                                movies={dramaSeries}
                                title="🎭 Drama Series"
                                autoScroll={false}
                        />
                        
                        <HorizontalCarousel 
                                movies={comedySeries}
                                title="😄 Comedy Series"
                                autoScroll={false}
                        />
                        
                        <HorizontalCarousel 
                                movies={sciFiSeries}
                                title="🚀 Sci-Fi Series"
                                autoScroll={false}
                        />
                </div>

                <!-- Browse All Series Section -->
                <div class="border-t border-slate-200 pt-8">
                        <h2 class="text-2xl md:text-3xl font-bold text-slate-800 mb-6">Browse All Series</h2>
                        
                        <!-- Search and Filter -->
                        <div class="mb-8 space-y-4 md:space-y-0 md:flex md:items-center md:justify-between">
                                <div class="relative">
                                        <input
                                                type="text"
                                                placeholder="Search series..."
                                                bind:value={searchTerm}
                                                class="w-full md:w-80 px-4 py-3 pl-12 bg-white border border-slate-300 rounded-lg text-slate-800 placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent shadow-sm"
                                        >
                                        <svg class="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                        </svg>
                                </div>
                                
                                <div class="flex flex-wrap gap-2">
                                        {#each genres as genre}
                                                <button
                                                        class="px-4 py-2 rounded-full transition-all duration-300 {selectedGenre === genre 
                                                                ? 'bg-blue-500 text-white shadow-md' 
                                                                : 'bg-white text-slate-600 hover:bg-slate-50 border border-slate-200'}"
                                                        on:click={() => selectedGenre = genre}
                                                >
                                                        {genre.charAt(0).toUpperCase() + genre.slice(1)}
                                                </button>
                                        {/each}
                                </div>
                        </div>

                <!-- Series Grid -->
                {#if filteredSeries.length > 0}
                        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4 md:gap-6">
                                {#each filteredSeries as series}
                                        <MovieCard movie={series} />
                                {/each}
                        </div>
                        {:else}
                                <div class="text-center py-16">
                                        <div class="text-6xl mb-4">📺</div>
                                        <h3 class="text-2xl font-semibold text-slate-800 mb-2">No series found</h3>
                                        <p class="text-slate-500">Try adjusting your search or filter criteria</p>
                                </div>
                        {/if}

                        <!-- Load More Button -->
                        {#if filteredSeries.length > 0}
                                <div class="text-center mt-12">
                                        <button class="bg-blue-500 hover:bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold transition-all duration-300 shadow-md hover:shadow-lg">
                                                Load More Series
                                        </button>
                                </div>
                        {/if}
                </div>
        </div>
</div>
