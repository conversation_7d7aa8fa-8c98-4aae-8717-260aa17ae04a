import { json } from '@sveltejs/kit';
import { movieApi } from '$lib/services/movieApi.js';

/** @type {import('./$types').RequestHandler} */
export async function GET({ url }) {
    try {
        const category = url.searchParams.get('category') || 'popular';
        const page = parseInt(url.searchParams.get('page') || '1');

        let series = [];

        switch (category) {
            case 'popular':
                series = await movieApi.getPopularSeries(page);
                break;
            case 'top_rated':
                series = await movieApi.getTopRatedSeries(page);
                break;
            case 'trending':
                series = await movieApi.getTrending('tv');
                break;
            default:
                series = await movieApi.getPopularSeries(page);
        }

        return json({
            success: true,
            data: series,
            page,
            category
        });

    } catch (error) {
        console.error('Series API Error:', error);
        return json({
            success: false,
            error: error.message
        }, { status: 500 });
    }
}
