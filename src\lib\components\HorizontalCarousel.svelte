<script>
        import { onMount } from 'svelte';
        import MovieCard from './MovieCard.svelte';
        
        export let movies = [];
        export let title = "Featured Movies";
        export let autoScroll = false;
        export let scrollInterval = 3000;
        
        let carousel;
        let currentIndex = 0;
        let autoScrollTimer;
        let isPaused = false;
        
        const itemWidth = 300; // Approximate width of each movie card
        const visibleItems = 5; // Number of visible items at once
        
        function scrollToIndex(index) {
                if (carousel) {
                        const scrollLeft = index * itemWidth;
                        carousel.scrollTo({ left: scrollLeft, behavior: 'smooth' });
                        currentIndex = index;
                }
        }
        
        function scrollLeft() {
                const newIndex = Math.max(0, currentIndex - 1);
                scrollToIndex(newIndex);
        }
        
        function scrollRight() {
                const maxIndex = Math.max(0, movies.length - visibleItems);
                const newIndex = Math.min(maxIndex, currentIndex + 1);
                scrollToIndex(newIndex);
        }
        
        function startAutoScroll() {
                if (!autoScroll || isPaused) return;
                
                autoScrollTimer = setInterval(() => {
                        const maxIndex = Math.max(0, movies.length - visibleItems);
                        if (currentIndex >= maxIndex) {
                                scrollToIndex(0);
                        } else {
                                scrollRight();
                        }
                }, scrollInterval);
        }
        
        function stopAutoScroll() {
                if (autoScrollTimer) {
                        clearInterval(autoScrollTimer);
                        autoScrollTimer = null;
                }
        }
        
        function pauseAutoScroll() {
                isPaused = true;
                stopAutoScroll();
        }
        
        function resumeAutoScroll() {
                isPaused = false;
                startAutoScroll();
        }
        
        onMount(() => {
                if (autoScroll) startAutoScroll();
                return stopAutoScroll;
        });
</script>

<div class="relative" on:mouseenter={pauseAutoScroll} on:mouseleave={resumeAutoScroll}>
        <!-- Title -->
        <div class="flex items-center justify-between mb-6">
                <h2 class="text-2xl md:text-3xl font-bold text-slate-800">{title}</h2>
                <div class="flex space-x-2">
                        <button
                                class="p-2 rounded-full bg-white border border-slate-200 hover:border-blue-500 hover:bg-blue-50 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
                                on:click={scrollLeft}
                                disabled={currentIndex === 0}
                                aria-label="Scroll left"
                        >
                                <svg class="w-5 h-5 text-slate-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                                </svg>
                        </button>
                        <button
                                class="p-2 rounded-full bg-white border border-slate-200 hover:border-blue-500 hover:bg-blue-50 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
                                on:click={scrollRight}
                                disabled={currentIndex >= Math.max(0, movies.length - visibleItems)}
                                aria-label="Scroll right"
                        >
                                <svg class="w-5 h-5 text-slate-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                        </button>
                </div>
        </div>
        
        <!-- Carousel Container -->
        <div class="relative overflow-hidden">
                <div
                        bind:this={carousel}
                        class="flex space-x-6 overflow-x-auto scrollbar-hide scroll-smooth pb-4"
                        style="scroll-snap-type: x mandatory;"
                >
                        {#each movies as movie}
                                <div class="flex-none w-72" style="scroll-snap-align: start;">
                                        <MovieCard {movie} />
                                </div>
                        {/each}
                </div>
        </div>
        
        <!-- Progress Indicators -->
        {#if movies.length > visibleItems}
                <div class="flex justify-center mt-4 space-x-2">
                        {#each Array(Math.ceil(movies.length / visibleItems)) as _, index}
                                <button
                                        class="w-3 h-3 rounded-full transition-all duration-300 {Math.floor(currentIndex / visibleItems) === index 
                                                ? 'bg-blue-500 scale-110' 
                                                : 'bg-slate-300 hover:bg-slate-400'}"
                                        on:click={() => scrollToIndex(index * visibleItems)}
                                        aria-label={`Go to page ${index + 1}`}
                                ></button>
                        {/each}
                </div>
        {/if}
</div>

<style>
        .scrollbar-hide {
                -ms-overflow-style: none;
                scrollbar-width: none;
        }
        .scrollbar-hide::-webkit-scrollbar {
                display: none;
        }
        
        @media (max-width: 768px) {
                .scrollbar-hide {
                        scroll-snap-type: none;
                }
        }
</style>