type DynamicRoutes = {
	
};

type Layouts = {
	"/": undefined;
	"/about": undefined;
	"/movies": undefined;
	"/series": undefined
};

export type RouteId = "/" | "/about" | "/movies" | "/series";

export type RouteParams<T extends RouteId> = T extends keyof DynamicRoutes ? DynamicRoutes[T] : Record<string, never>;

export type LayoutParams<T extends RouteId> = Layouts[T] | Record<string, never>;

export type Pathname = "/" | "/about" | "/movies" | "/series";

export type ResolvedPathname = `${"" | `/${string}`}${Pathname}`;

export type Asset = "/app.css";