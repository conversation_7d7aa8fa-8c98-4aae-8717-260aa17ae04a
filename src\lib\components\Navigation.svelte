<script>
        import { page } from '$app/stores';
        
        const navItems = [
                { name: 'Home', path: '/', icon: 'home' },
                { name: 'Movies', path: '/movies', icon: 'film' },
                { name: 'Series', path: '/series', icon: 'tv' },
                { name: 'About', path: '/about', icon: 'info' }
        ];
        
        function getIconSVG(iconName) {
                const icons = {
                        home: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>',
                        film: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>',
                        tv: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>',
                        info: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>'
                };
                return icons[iconName] || '';
        }
</script>

<!-- Desktop Navigation -->
<nav class="hidden md:block fixed top-0 left-0 right-0 z-50 bg-white/95 backdrop-blur-md border-b border-slate-200 shadow-sm">
        <div class="max-w-7xl mx-auto px-4 md:px-8">
                <div class="flex items-center justify-between h-16">
                        <!-- Logo -->
                        <div class="flex items-center">
                                <h1 class="text-2xl font-bold text-slate-800">CineMax</h1>
                        </div>
                        
                        <!-- Navigation Links -->
                        <div class="flex space-x-8">
                                {#each navItems as item}
                                        <a 
                                                href={item.path}
                                                class="flex items-center space-x-2 px-3 py-2 rounded-lg transition-all duration-300 hover:bg-slate-50 {$page.url.pathname === item.path ? 'text-blue-500 bg-blue-50' : 'text-slate-600 hover:text-slate-800'}"
                                        >
                                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        {@html getIconSVG(item.icon)}
                                                </svg>
                                                <span class="font-medium">{item.name}</span>
                                        </a>
                                {/each}
                        </div>
                        
                        <!-- User Menu -->
                        <div class="flex items-center space-x-4">
                                <button class="text-slate-500 hover:text-slate-700 transition-colors" aria-label="Search">
                                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                        </svg>
                                </button>
                        </div>
                </div>
        </div>
</nav>

<!-- Mobile Bottom Navigation -->
<nav class="md:hidden fixed bottom-0 left-0 right-0 z-50 bg-white/95 backdrop-blur-md border-t border-slate-200 shadow-lg">
        <div class="flex items-center justify-around h-16 px-4">
                {#each navItems as item}
                        <a 
                                href={item.path}
                                class="flex flex-col items-center justify-center space-y-1 py-2 px-3 rounded-lg transition-all duration-300 {$page.url.pathname === item.path ? 'text-blue-500' : 'text-slate-400 hover:text-slate-600'}"
                        >
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        {@html getIconSVG(item.icon)}
                                </svg>
                                <span class="text-xs font-medium">{item.name}</span>
                        </a>
                {/each}
        </div>
</nav>

<!-- Add top padding for desktop -->
<style>
        :global(body) {
                padding-top: 0;
        }
        
        @media (min-width: 768px) {
                :global(body) {
                        padding-top: 4rem;
                }
        }
</style>
