import { OMDB_API_KEY, OMDB_BASE_URL } from '$env/static/private';

// OMDB API service
class MovieApiService {
    constructor() {
        this.baseUrl = OMDB_BASE_URL;
        this.apiKey = OMDB_API_KEY;
    }

    // Helper method to make API requests
    async makeRequest(params = {}) {
        const url = new URL(this.baseUrl);
        url.searchParams.append('apikey', this.apiKey);

        // Add additional parameters
        Object.entries(params).forEach(([key, value]) => {
            if (value !== undefined && value !== null) {
                url.searchParams.append(key, value.toString());
            }
        });

        try {
            const response = await fetch(url.toString());
            if (!response.ok) {
                throw new Error(`API request failed: ${response.status} ${response.statusText}`);
            }
            const data = await response.json();
            if (data.Response === 'False') {
                throw new Error(data.Error || 'API request failed');
            }
            return data;
        } catch (error) {
            console.error('Movie API Error:', error);
            throw error;
        }
    }

    // Transform OMDB movie data to match your current structure
    transformMovieData(omdbMovie) {
        return {
            id: omdbMovie.imdbID,
            title: omdbMovie.Title,
            year: parseInt(omdbMovie.Year),
            genre: omdbMovie.Genre ? omdbMovie.Genre.split(',')[0].trim() : 'Unknown',
            rating: omdbMovie.imdbRating !== 'N/A' ? parseFloat(omdbMovie.imdbRating) : 0,
            duration: omdbMovie.Runtime !== 'N/A' ? omdbMovie.Runtime : 'N/A',
            quality: omdbMovie.imdbRating >= 8 ? '4K' : 'HD',
            poster: omdbMovie.Poster !== 'N/A' ? omdbMovie.Poster : null,
            overview: omdbMovie.Plot !== 'N/A' ? omdbMovie.Plot : '',
            director: omdbMovie.Director !== 'N/A' ? omdbMovie.Director : '',
            actors: omdbMovie.Actors !== 'N/A' ? omdbMovie.Actors : ''
        };
    }

    // Transform OMDB series data to match your current structure
    transformSeriesData(omdbSeries) {
        return {
            id: omdbSeries.imdbID,
            title: omdbSeries.Title,
            year: parseInt(omdbSeries.Year),
            genre: omdbSeries.Genre ? omdbSeries.Genre.split(',')[0].trim() : 'Unknown',
            rating: omdbSeries.imdbRating !== 'N/A' ? parseFloat(omdbSeries.imdbRating) : 0,
            duration: omdbSeries.totalSeasons ? `${omdbSeries.totalSeasons} Season${omdbSeries.totalSeasons > 1 ? 's' : ''}` : 'N/A',
            quality: omdbSeries.imdbRating >= 8 ? '4K' : 'HD',
            poster: omdbSeries.Poster !== 'N/A' ? omdbSeries.Poster : null,
            overview: omdbSeries.Plot !== 'N/A' ? omdbSeries.Plot : '',
            director: omdbSeries.Director !== 'N/A' ? omdbSeries.Director : '',
            actors: omdbSeries.Actors !== 'N/A' ? omdbSeries.Actors : ''
        };
    }

    // Get movie by ID
    async getMovieById(imdbId) {
        const data = await this.makeRequest({ i: imdbId });
        return this.transformMovieData(data);
    }

    // Get series by ID
    async getSeriesById(imdbId) {
        const data = await this.makeRequest({ i: imdbId });
        return this.transformSeriesData(data);
    }

    // Search movies and series
    async search(query, type = '', page = 1) {
        const data = await this.makeRequest({
            s: query,
            type: type, // 'movie', 'series', or '' for all
            page: page
        });

        if (data.Search) {
            return data.Search.map(item => {
                if (item.Type === 'movie') {
                    return this.transformMovieData(item);
                } else if (item.Type === 'series') {
                    return this.transformSeriesData(item);
                }
                return item;
            });
        }
        return [];
    }

    // Search only movies
    async searchMovies(query, page = 1) {
        return this.search(query, 'movie', page);
    }

    // Search only series
    async searchSeries(query, page = 1) {
        return this.search(query, 'series', page);
    }

    // Get popular movies (using predefined popular movie titles)
    async getPopularMovies() {
        const popularTitles = [
            'The Dark Knight', 'Inception', 'Interstellar', 'The Matrix',
            'Pulp Fiction', 'The Godfather', 'Forrest Gump', 'The Shawshank Redemption',
            'Avatar', 'Titanic', 'Star Wars', 'Jurassic Park', 'The Avengers', 'Iron Man'
        ];

        const movies = [];
        for (const title of popularTitles.slice(0, 10)) {
            try {
                const results = await this.search(title, 'movie');
                if (results.length > 0) {
                    const detailedMovie = await this.getMovieById(results[0].id);
                    movies.push(detailedMovie);
                }
            } catch (error) {
                console.warn(`Failed to fetch movie: ${title}`, error);
            }
        }
        return movies;
    }

    // Get popular series (using predefined popular series titles)
    async getPopularSeries() {
        const popularTitles = [
            'Breaking Bad', 'Game of Thrones', 'The Office', 'Friends',
            'Stranger Things', 'The Crown', 'House of Cards', 'Sherlock',
            'The Mandalorian', 'Westworld', 'Black Mirror', 'Better Call Saul'
        ];

        const series = [];
        for (const title of popularTitles.slice(0, 8)) {
            try {
                const results = await this.search(title, 'series');
                if (results.length > 0) {
                    const detailedSeries = await this.getSeriesById(results[0].id);
                    series.push(detailedSeries);
                }
            } catch (error) {
                console.warn(`Failed to fetch series: ${title}`, error);
            }
        }
        return series;
    }

    // Get movies by genre (search-based approach)
    async getMoviesByGenre(genre) {
        const genreQueries = {
            'action': ['Mission Impossible', 'John Wick', 'Fast and Furious', 'Die Hard'],
            'drama': ['The Godfather', 'Forrest Gump', 'The Shawshank Redemption', 'Goodfellas'],
            'sci-fi': ['Blade Runner', 'The Matrix', 'Inception', 'Interstellar'],
            'comedy': ['The Hangover', 'Superbad', 'Anchorman', 'Dumb and Dumber'],
            'horror': ['The Exorcist', 'Halloween', 'A Nightmare on Elm Street', 'The Shining']
        };

        const queries = genreQueries[genre.toLowerCase()] || genreQueries['action'];
        const movies = [];

        for (const query of queries) {
            try {
                const results = await this.search(query, 'movie');
                if (results.length > 0) {
                    const detailedMovie = await this.getMovieById(results[0].id);
                    movies.push(detailedMovie);
                }
            } catch (error) {
                console.warn(`Failed to fetch movie: ${query}`, error);
            }
        }
        return movies;
    }
}

export const movieApi = new MovieApiService();
