import { json } from '@sveltejs/kit';
import { movieApi } from '$lib/services/movieApi.js';

/** @type {import('./$types').RequestHandler} */
export async function GET({ url }) {
    try {
        const category = url.searchParams.get('category') || 'popular';
        const page = parseInt(url.searchParams.get('page') || '1');
        const genre = url.searchParams.get('genre');

        let movies = [];

        switch (category) {
            case 'popular':
                movies = await movieApi.getPopularMovies();
                break;
            case 'by_genre':
                if (genre) {
                    movies = await movieApi.getMoviesByGenre(genre);
                } else {
                    throw new Error('Genre name required for by_genre category');
                }
                break;
            default:
                movies = await movieApi.getPopularMovies();
        }

        return json({
            success: true,
            data: movies,
            page,
            category
        });

    } catch (error) {
        console.error('Movies API Error:', error);
        return json({
            success: false,
            error: error.message
        }, { status: 500 });
    }
}
